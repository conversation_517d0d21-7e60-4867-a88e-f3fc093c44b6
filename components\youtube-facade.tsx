"use client";

import { useState } from "react";
import { Play } from "lucide-react";
import Image from "next/image";

interface YouTubeFacadeProps {
  videoId: string;
  title: string;
  className?: string;
  thumbnailQuality?: 'default' | 'mqdefault' | 'hqdefault' | 'sddefault' | 'maxresdefault';
}

export function YouTubeFacade({ 
  videoId, 
  title, 
  className = "",
  thumbnailQuality = 'hqdefault'
}: YouTubeFacadeProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/${thumbnailQuality}.jpg`;

  const handlePlay = () => {
    setIsLoaded(true);
  };

  if (isLoaded) {
    return (
      <div className={`relative w-full ${className}`}>
        <iframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="w-full h-full rounded-lg"
          loading="lazy"
        />
      </div>
    );
  }

  return (
    <div 
      className={`relative w-full cursor-pointer group ${className}`}
      onClick={handlePlay}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handlePlay();
        }
      }}
      aria-label={`Lire la vidéo: ${title}`}
    >
      {/* Thumbnail */}
      <div className="relative w-full h-full overflow-hidden rounded-lg">
        <Image
          src={thumbnailUrl}
          alt={`Miniature de la vidéo: ${title}`}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={false}
        />
        
        {/* Overlay sombre */}
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
        
        {/* Bouton Play */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-red-600 hover:bg-red-700 text-white rounded-full p-4 shadow-lg transform transition-all duration-300 group-hover:scale-110">
            <Play className="w-8 h-8 ml-1" fill="currentColor" />
          </div>
        </div>
        
        {/* Badge YouTube */}
        <div className="absolute top-3 right-3 bg-red-600 text-white px-2 py-1 rounded text-sm font-semibold">
          YouTube
        </div>
      </div>
      
      {/* Titre de la vidéo */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 rounded-b-lg">
        <h3 className="text-white font-semibold text-sm md:text-base line-clamp-2">
          {title}
        </h3>
      </div>
    </div>
  );
}
